"use client"

import { useState, useEffect } from "react"
import ReviewForm, { ReviewData } from "../../../components/ReviewForm/ReviewForm"
import { ReviewList } from "./components/review-list.tsx"
import { <PERSON>, CardBody, CardHeader, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from "@heroui/react"
import { getReviewsByServiceId, createReview, CreateReviewData, Review as BackendReview } from "../../../../service/reviewService"
import { useAuth } from "react-oidc-context"
import { toast } from "react-toastify"
import { useLocation } from "react-router-dom"

// Local interfaces for the component (mapped from backend Review interface)
export interface Review {
  id: string
  customerName: string
  customerAvatar?: string
  rating: number
  title: string
  content: string
  date: string
  replies: Reply[]
}

export interface Reply {
  id: string
  authorName: string
  authorAvatar?: string
  content: string
  date: string
  isBusinessOwner?: boolean
}

// Props interface for the component
interface ReviewServiceProps {
  serviceId: string
  providerId?: string
}

export default function ReviewService({ serviceId, providerId }: ReviewServiceProps) {
  const auth = useAuth()
  const location = useLocation()
  const [reviews, setReviews] = useState<Review[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isReviewFormOpen, setIsReviewFormOpen] = useState(false)
  const [selectedTab, setSelectedTab] = useState("reviews")

  // Function to map backend Review to local Review interface
  const mapBackendReview = (backendReview: BackendReview): Review => ({
    id: backendReview._id || backendReview.id || 'unknown',
    customerName: backendReview.userName || backendReview.name || 'Anonymous',
    customerAvatar: backendReview.userProfileImage || backendReview.profileImage,
    rating: backendReview.rating || 0,
    title: backendReview.title || 'No title',
    content: backendReview.comment || backendReview.review || 'No content',
    date: new Date(backendReview.createdAt || backendReview.date || Date.now()).toLocaleDateString(),
    replies: [] // TODO: Add replies support when backend supports it
  })

  // Check URL hash to determine initial tab
  useEffect(() => {
    if (location.hash === '#reviews') {
      setSelectedTab('reviews')
    }
  }, [location.hash])

  // Fetch reviews for the service
  useEffect(() => {
    const fetchReviews = async () => {
      if (!serviceId) return

      try {
        setLoading(true)
        setError(null)
        const response = await getReviewsByServiceId(serviceId, { page: 1, limit: 50 })
        const mappedReviews = response.reviews.map(mapBackendReview)
        setReviews(mappedReviews)
      } catch (err) {
        console.error('Error fetching reviews:', err)
        setError('Failed to load reviews')
      } finally {
        setLoading(false)
      }
    }

    fetchReviews()
  }, [serviceId])

  const handleNewReview = async (reviewData: ReviewData) => {
    if (!auth.isAuthenticated) {
      const errorMessage = 'You must be logged in to write a review'
      setError(errorMessage)
      toast.error(errorMessage)
      return
    }

    try {
      const createData: CreateReviewData = {
        serviceId: serviceId,
        providerId: providerId || 'unknown-provider',
        bookingId: 'booking-' + Date.now(), // TODO: Get actual booking ID
        title: reviewData.title,
        review: reviewData.review,
        comment: reviewData.review,
        rating: reviewData.rating, // Overall calculated rating
        // Individual ratings (1-4 scale)
        serviceRating: reviewData.serviceRating,
        qualityRating: reviewData.qualityRating,
        valueRating: reviewData.valueRating,
        communicationRating: reviewData.communicationRating,
        timelinessRating: reviewData.timelinessRating,
        date: new Date().toISOString(),
        userName: auth.user?.profile?.name || 'Anonymous',
        userEmail: auth.user?.profile?.email || '',
        userProfileImage: auth.user?.profile?.picture || '',
        imageNames: reviewData.imageNames || [],
        imageUrls: reviewData.imageUrls || []
      }

      const createdReview = await createReview(createData)
      const mappedReview = mapBackendReview(createdReview)
      setReviews([mappedReview, ...reviews])
      toast.success('Review submitted successfully!')
      setError(null) // Clear any previous errors
      setIsReviewFormOpen(false) // Close the modal
    } catch (err) {
      console.error('Error creating review:', err)
      const errorMessage = 'Failed to submit review. Please try again.'
      setError(errorMessage)
      toast.error(errorMessage)
    }
  }

  const handleNewReply = (reviewId: string, reply: Omit<Reply, "id">) => {
    const newReply: Reply = {
      ...reply,
      id: Date.now().toString(),
    }

    setReviews(
      reviews.map((review) =>
        review.id === reviewId ? { ...review, replies: [...review.replies, newReply] } : review,
      ),
    )
  }

  const averageRating =
    reviews.length > 0 ? (reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length).toFixed(1) : "0.0"

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <div className="text-center py-12">
          <p className="text-gray-500">Loading reviews...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <div className="text-center py-12">
          <p className="text-red-500">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Retry
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Customer Reviews</h1>
        <p className="text-gray-600">Share your experience and read what others have to say</p>
      </div>

      <div className="grid gap-6 mb-8">
        <Card>
          <CardHeader>
            <div>
              <h3 className="text-lg font-semibold">Review Summary</h3>
              <p className="text-sm text-gray-600">
                {reviews.length} reviews • Average rating: {averageRating}/4.0
              </p>
            </div>
          </CardHeader>
          <CardBody>
            <div className="flex items-center gap-4">
              <div className="text-3xl font-bold">{averageRating}</div>
              <div className="flex-1">
                {[4, 3, 2, 1].map((rating) => {
                  const count = reviews.filter((r) => r.rating === rating).length
                  const percentage = reviews.length > 0 ? (count / reviews.length) * 100 : 0
                  return (
                    <div key={rating} className="flex items-center gap-2 text-sm">
                      <span className="w-3">{rating}</span>
                      <div className="flex-1 bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-blue-500 h-2 rounded-full transition-all"
                          style={{ width: `${percentage}%` }}
                        />
                      </div>
                      <span className="w-8 text-gray-500">{count}</span>
                    </div>
                  )
                })}
              </div>
            </div>
          </CardBody>
        </Card>
      </div>

      <Tabs
        selectedKey={selectedTab}
        onSelectionChange={(key) => setSelectedTab(key as string)}
        className="space-y-6"
      >
        <Tab key="reviews" title="All Reviews">
          <div className="space-y-6">
            <ReviewList reviews={reviews} onReply={handleNewReply} />
          </div>
        </Tab>
        <Tab key="write" title="Write a Review">
          <div className="flex flex-col items-center justify-center py-12 space-y-4">
            <h3 className="text-xl font-semibold text-gray-800">Share Your Experience</h3>
            <p className="text-gray-600 text-center max-w-md">
              Help others make informed decisions by sharing your experience with this service.
            </p>
            <Button
              color="primary"
              size="lg"
              onPress={() => setIsReviewFormOpen(true)}
              className="px-8"
            >
              Write a Review
            </Button>
          </div>
        </Tab>
      </Tabs>

      {/* Review Form Modal */}
      <ReviewForm
        isOpen={isReviewFormOpen}
        onClose={() => setIsReviewFormOpen(false)}
        onSubmit={handleNewReview}
        serviceId={serviceId}
        providerId={providerId}
        serviceName="Service" // You can pass the actual service name if available
      />
    </div>
  )
}

