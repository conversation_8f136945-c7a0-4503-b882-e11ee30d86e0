import { useState, useCallback, useEffect, useMemo } from 'react';
import { useAuth } from 'react-oidc-context';
import BreadCrumb from '../../../components/common/breadcrumb/breadCrumb';
import CustomButton from '../../../components/CustomButton';
import ReviewForm, { ReviewData } from '../../../../feature-module/components/ReviewForm/ReviewForm';
import RescheduleForm, { RescheduleData } from '../../../../feature-module/components/RescheduleForm/RescheduleForm';
import { useNavigate } from 'react-router-dom';
import { Modal, ModalContent, ModalHeader, ModalBody, ModalFooter, Button, Chip, Divider, Spinner } from '@heroui/react';
import { FaEye, FaCalendarAlt, FaMapMarkerAlt, FaUser, FaEnvelope, FaPhone, FaDollarSign, FaHome, FaStickyNote, FaCog, FaTag } from 'react-icons/fa';
import { apiClient } from '../../../../api';
import { createReview, CreateReviewData } from '../../../../service/reviewService';
import {
  getUserBookings,
  confirmBooking,
  finishBooking,
  cancelBooking,
  getBookingById,
  Booking as BookingType,
  BookingsResponse
} from '../../../../service/bookingService';
import { toast } from 'react-toastify';

// User data interface
interface UserData {
  name?: string;
  username?: string;
  email?: string;
  profileImage?: string;
}

const BookingList = () => {
  const auth = useAuth();
  const navigate = useNavigate();
  const [showReviewForm, setShowReviewForm] = useState(false);
  const [showRescheduleForm, setShowRescheduleForm] = useState(false);
  const [showBookingDetails, setShowBookingDetails] = useState(false);
  const [selectedBooking, setSelectedBooking] = useState<Booking | null>(null);
  const [bookingDetails, setBookingDetails] = useState<BookingType | null>(null);
  const [detailsLoading, setDetailsLoading] = useState(false);
  const [userData, setUserData] = useState<UserData>({});
  const [userDataLoading, setUserDataLoading] = useState(false);
  const [bookingsLoading, setBookingsLoading] = useState(false);
  const [bookingsError, setBookingsError] = useState<string | null>(null);


  // Initial bookings data (fallback when API is not available)
  const initialBookings: Booking[] = useMemo(() => [
    {
      id: 1,
      service: 'Computer Services',
      status: 'Cancelled' as const,
      date: '27 Sep 2022, 17:00-18:00',
      amount: '$100.00',
      payment: 'PayPal',
      location: 'Newark, USA',
      provider: 'John Doe',
      email: '<EMAIL>',
      phone: '******-888-8888',
      actions: ['View Details', 'Reschedule'],
      providerId: 'provider-1',
      bookingId: 'booking-1',
    },
    {
      id: 2,
      service: 'Car Repair Services',
      status: 'Completed' as const,
      date: '23 Sep 2022, 10:00-11:00',
      amount: '$50.00',
      payment: 'COD',
      location: 'Alabama, USA',
      provider: 'John Smith',
      email: '<EMAIL>',
      phone: '******-275-5393',
      actions: ['View Details', 'Rebook', 'Add Review'],
      providerId: 'provider-2',
      bookingId: 'booking-2',
    },
    {
      id: 3,
      service: 'Interior Designing',
      status: 'Inprogress' as const,
      date: '22 Sep 2022, 11:00-12:00',
      amount: '$50.00',
      payment: 'PayPal',
      location: 'Washington, DC, USA',
      provider: 'Quentin',
      email: '<EMAIL>',
      phone: '******-810-9218',
      actions: ['View Details', 'Chat', 'Cancel'],
      providerId: 'provider-3',
      bookingId: 'booking-3',
    },
  ], []);

  // Get user ID from auth context
  const getUserId = useCallback(() => {
    if (auth.user) {
      return auth.user.profile.preferred_username ||
             auth.user.profile.sub ||
             auth.user.profile.email;
    }
    return null;
  }, [auth.user]);



  // Fetch user data from API
  const fetchUserData = useCallback(async () => {
    const uid = getUserId();
    if (!uid || !auth.isAuthenticated) {
      console.log('Cannot fetch user data: No user ID or not authenticated');
      return;
    }

    try {
      setUserDataLoading(true);
      console.log(`Fetching user data for ID: ${uid}`);

      const response = await apiClient.get(`/api/v1/user/${uid}`);

      if (response.data) {
        console.log('User data fetched successfully:', response.data);
        setUserData(response.data);
      }
    } catch (error) {
      console.error('Error fetching user data:', error);
      // Fallback to auth profile data
      if (auth.user) {
        const fallbackData: UserData = {
          name: auth.user.profile.name ||
                auth.user.profile.given_name ||
                auth.user.profile.preferred_username ||
                auth.user.profile.email?.split('@')[0] ||
                'User',
          email: auth.user.profile.email,
          profileImage: 'https://via.placeholder.com/40'
        };
        setUserData(fallbackData);
      }
    } finally {
      setUserDataLoading(false);
    }
  }, [getUserId, auth.isAuthenticated, auth.user]);

  // Fetch bookings data from API
  const fetchBookings = useCallback(async () => {
    const uid = getUserId();
    if (!uid || !auth.isAuthenticated) {
      console.log('Cannot fetch bookings: No user ID or not authenticated');
      return;
    }

    try {
      setBookingsLoading(true);
      setBookingsError(null);
      console.log(`Fetching bookings for user ID: ${uid}`);

      const response = await getUserBookings({ userId: uid });

      if (response && response.bookings) {
        console.log('Bookings fetched successfully:', response.bookings);
        console.log('Sample booking data structure:', response.bookings[0]);
        // Transform API data to match component interface
        const transformedBookings = response.bookings.map((booking: BookingType) => ({
          id: booking.id,
          service: booking.serviceName || booking.service,
          serviceName: booking.serviceName || booking.service,
          status: booking.status,
          date: booking.date,
          amount: booking.amount,
          payment: booking.paymentMethod || booking.payment,
          paymentMethod: booking.paymentMethod || booking.payment,
          location: booking.location,
          provider: booking.provider,
          email: booking.email,
          phone: booking.phone,
          providerId: booking.providerId,
          bookingId: booking.bookingId || booking.id.toString(),
          serviceId: booking.serviceId,
          serviceImages: booking.serviceImages || [],
          description: booking.description,
          duration: booking.duration,
          category: booking.category,
          personalInfo: booking.personalInfo,
          serviceDetails: booking.serviceDetails,
          referenceCode: booking.referenceCode,
          additionalServices: booking.additionalServices || [],
          appointmentTimeFrom: booking.appointmentTimeFrom,
          appointmentTimeTo: booking.appointmentTimeTo,
          appointmentDate: booking.appointmentDate,
          bookingStatus: booking.bookingStatus,
          actions: getActionsForStatus(booking.status)
        }));
        setBookings(transformedBookings);
      } else {
        console.log('No bookings found, using fallback data');
        setBookings(initialBookings);
      }
    } catch (error) {
      console.error('Error fetching bookings:', error);
      setBookingsError('Failed to load bookings. Please try again.');
      // Fallback to initial bookings on error
      setBookings(initialBookings);
    } finally {
      setBookingsLoading(false);
    }
  }, [getUserId, auth.isAuthenticated, initialBookings]);

  // Helper function to determine actions based on booking status
  const getActionsForStatus = (status: string): string[] => {
    const baseActions = ['View Details'];
    switch (status) {
      case 'Pending':
        return [...baseActions, 'Confirm', 'Cancel'];
      case 'Confirmed':
        return [...baseActions, 'Add Review', 'Reschedule', 'Cancel'];
      case 'Finished':
        return [...baseActions, 'View Service Reviews', 'Rebook'];
      case 'Cancelled':
        return [...baseActions, 'Reschedule'];
      case 'Completed':
        return [...baseActions, 'Add Review', 'View Service Reviews', 'Rebook'];
      case 'Inprogress':
        return [...baseActions, 'Chat', 'Cancel'];
      case 'Rescheduled':
        return [...baseActions, 'Add to Calendar'];
      default:
        return [...baseActions, 'Chat'];
    }
  };

  // Fetch user data and bookings when component mounts
  useEffect(() => {
    if (auth.isAuthenticated && !auth.isLoading) {
      fetchUserData();
      fetchBookings();
    }
  }, [auth.isAuthenticated, auth.isLoading, fetchUserData, fetchBookings]);

  // State to manage bookings
  const [bookings, setBookings] = useState<Booking[]>(initialBookings);

  type ButtonColor = 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger';
  type ButtonVariant = 'solid' | 'bordered' | 'light' | 'flat' | 'faded' | 'shadow' | 'ghost';

  // Define a type for booking (updated to match service interface)
  interface Booking {
    id: number | string;
    service: string;
    serviceName?: string; // Alternative field name for service
    status: 'Pending' | 'Confirmed' | 'Finished' | 'Cancelled' | 'Completed' | 'Inprogress' | 'Rescheduled';
    date: string;
    amount: string;
    payment: string;
    paymentMethod?: string; // Alternative field name for payment
    location: string;
    provider: string;
    email: string;
    phone: string;
    actions: string[];
    providerId?: string;
    bookingId?: string;
    serviceId?: string;
    serviceImages?: string[]; // Array of service image URLs
    description?: string; // Service description
    duration?: string; // Service duration
    category?: string; // Service category
    personalInfo?: {
      firstName?: string;
      lastName?: string;
      email?: string;
      phone?: string;
      streetAddress?: string;
      city?: string;
      state?: string;
      postalCode?: string;
      notes?: string;
      address?: {
        street?: string;
        city?: string;
        state?: string;
        postalCode?: string;
        country?: string;
      };
    };
    serviceDetails?: {
      serviceTitle?: string;
      categoryId?: string;
      subCategoryId?: string;
      price?: number;
      isOffers?: boolean;
      isAdditional?: boolean;
      staff?: unknown;
      availableDate?: unknown;
    };
    referenceCode?: string; // Booking reference code
    additionalServices?: {
      id?: string;
      name?: string;
      price?: number;
      duration?: string;
      description?: string;
    }[]; // Array of additional services
    appointmentTimeFrom?: string; // Appointment start time
    appointmentTimeTo?: string; // Appointment end time
    appointmentDate?: string; // Appointment date
    bookingStatus?: string; // Detailed booking status information
  }

  // Function to handle opening the review form
  const handleAddReview = (booking: Booking) => {
    setSelectedBooking(booking);
    setShowReviewForm(true);
  };

  // Function to handle submitting a review
  const handleSubmitReview = async (reviewData: ReviewData) => {
    if (!selectedBooking) {
      console.error('No booking selected for review');
      return;
    }

    try {
      console.log('Review submitted:', reviewData);
      console.log('Image data in review:', {
        imageCount: reviewData.imageNames?.length || 0,
        imageNames: reviewData.imageNames,
        imageUrls: reviewData.imageUrls
      });

      // Prepare review data for API
      const createData: CreateReviewData = {
        providerId: selectedBooking.providerId || `provider-${selectedBooking.id}`,
        serviceId: selectedBooking.serviceId || selectedBooking.id.toString(),
        serviceName: selectedBooking.service,
        bookingId: selectedBooking.bookingId || `booking-${selectedBooking.id}`,
        title: reviewData.title || selectedBooking.service,
        review: reviewData.review,
        comment: reviewData.review, // Backend expects comment field
        rating: reviewData.rating,
        images: reviewData.images,
        imageUrls: reviewData.imageUrls || [],
        imageNames: reviewData.imageNames || [], // Include image names for backend storage
      };

      console.log('Prepared review data for API:', {
        ...createData,
        imageNamesCount: createData.imageNames?.length || 0,
        imageNames: createData.imageNames,
        ratingFields: {
          rating: createData.rating,
          serviceRating: createData.serviceRating,
          qualityRating: createData.qualityRating,
          valueRating: createData.valueRating,
          communicationRating: createData.communicationRating,
          timelinessRating: createData.timelinessRating
        }
      });

      // Backend requires all rating fields - provide defaults using overall rating if not set
      createData.serviceRating = (reviewData.serviceRating && reviewData.serviceRating >= 1 && reviewData.serviceRating <= 4)
        ? Math.round(reviewData.serviceRating)
        : Math.round(reviewData.rating);

      createData.qualityRating = (reviewData.qualityRating && reviewData.qualityRating >= 1 && reviewData.qualityRating <= 4)
        ? Math.round(reviewData.qualityRating)
        : Math.round(reviewData.rating);

      createData.valueRating = (reviewData.valueRating && reviewData.valueRating >= 1 && reviewData.valueRating <= 4)
        ? Math.round(reviewData.valueRating)
        : Math.round(reviewData.rating);

      createData.communicationRating = (reviewData.communicationRating && reviewData.communicationRating >= 1 && reviewData.communicationRating <= 4)
        ? Math.round(reviewData.communicationRating)
        : Math.round(reviewData.rating);

      createData.timelinessRating = (reviewData.timelinessRating && reviewData.timelinessRating >= 1 && reviewData.timelinessRating <= 4)
        ? Math.round(reviewData.timelinessRating)
        : Math.round(reviewData.rating);

      // Submit review to API
      await createReview(createData);

      // Update booking status to "Finished" after successful review submission
      if (selectedBooking.bookingId) {
        try {
          await finishBooking(selectedBooking.bookingId);
          console.log('Booking status updated to Finished');

          // Update local booking state
          setBookings(prevBookings =>
            prevBookings.map(booking =>
              booking.id === selectedBooking.id
                ? { ...booking, status: 'Finished', actions: ['View Details', 'View Service Reviews', 'Rebook'] }
                : booking
            )
          );
        } catch (statusError) {
          console.error('Error updating booking status:', statusError);
          // Don't fail the review submission if status update fails
        }
      }

      // Show success message with option to view reviews
      toast.success(
        <div>
          <p>Review submitted successfully!</p>
          <button
            onClick={() => handleViewServiceReviews(selectedBooking)}
            className="mt-2 text-blue-600 hover:text-blue-800 underline text-sm"
          >
            View Service Reviews
          </button>
        </div>,
        { autoClose: 5000 }
      );

      // Close the review form
      setShowReviewForm(false);
    } catch (error) {
      console.error('Error submitting review:', error);
      toast.error('Failed to submit review. Please try again.');
    }
  };

  // Function to handle viewing service reviews
  const handleViewServiceReviews = (booking: Booking) => {
    const serviceId = booking.serviceId || booking.id.toString();
    const serviceName = booking.serviceName || booking.service;

    // Navigate to service details page with reviews tab
    const serviceDetailsUrl = `/services/service-details/${encodeURIComponent(serviceId)}/${encodeURIComponent(serviceName)}#reviews`;
    navigate(serviceDetailsUrl);
  };

  // Function to handle rescheduling
  const handleReschedule = (booking: Booking) => {
    setSelectedBooking(booking);
    setShowRescheduleForm(true);
  };

  // Function to handle rescheduling submission
  const handleRescheduleSubmit = (rescheduleData: RescheduleData) => {
    try {
      // In a real app, you would send this data to your backend
      console.log('Reschedule data submitted:', rescheduleData);

      // Update the booking with the new date and time
      const updatedBookings = bookings.map(booking => {
        if (booking.id === rescheduleData.bookingId) {
          // Format the date
          const dateOptions: Intl.DateTimeFormatOptions = {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
          };
          const formattedDate = rescheduleData.newDate.toLocaleDateString('en-US', dateOptions);

          // Create the new date string
          const newDateString = `${formattedDate}, ${rescheduleData.newTime}`;

          // Update the booking status to "Rescheduled"
          return {
            ...booking,
            date: newDateString,
            status: 'Rescheduled' as const,
            // Add a notification action to allow user to add to calendar
            actions: [...booking.actions, 'Add to Calendar'].filter(action => action !== 'Reschedule'),
          };
        }
        return booking;
      });

      // Update the bookings state
      setBookings(updatedBookings);

      // Success is now handled in the RescheduleForm component with a visual notification
    } catch (error) {
      console.error('Error rescheduling booking:', error);
      alert('Failed to reschedule booking. Please try again.');
    }
  };

  // Function to handle confirming a booking
  const handleConfirmBooking = async (booking: Booking) => {
    if (!booking.bookingId) {
      toast.error('Booking ID not found');
      return;
    }

    try {
      await confirmBooking(booking.bookingId);

      // Update local booking state
      setBookings(prevBookings =>
        prevBookings.map(b =>
          b.id === booking.id
            ? { ...b, status: 'Confirmed' as const, actions: ['Review', 'Reschedule', 'Cancel'] }
            : b
        )
      );

      toast.success('Booking confirmed successfully!');
    } catch (error) {
      console.error('Error confirming booking:', error);
      toast.error('Failed to confirm booking. Please try again.');
    }
  };

  // Function to handle cancelling a booking
  const handleCancelBooking = async (booking: Booking) => {
    if (!booking.bookingId) {
      toast.error('Booking ID not found');
      return;
    }

    if (window.confirm('Are you sure you want to cancel this booking?')) {
      try {
        await cancelBooking(booking.bookingId);

        // Update local booking state
        setBookings(prevBookings =>
          prevBookings.map(b =>
            b.id === booking.id
              ? { ...b, status: 'Cancelled' as const, actions: ['View Details', 'Reschedule'] }
              : b
          )
        );

        toast.success('Booking cancelled successfully!');
      } catch (error) {
        console.error('Error cancelling booking:', error);
        toast.error('Failed to cancel booking. Please try again.');
      }
    }
  };

  // Function to handle viewing booking details
  const handleViewDetails = async (booking: Booking) => {
    if (!booking.bookingId) {
      toast.error('Booking ID not found');
      return;
    }

    try {
      setDetailsLoading(true);
      setSelectedBooking(booking);
      console.log('Fetching details for booking:', booking.bookingId);

      // Fetch detailed booking information from API
      const details = await getBookingById(booking.bookingId);

      // Log the complete booking details structure
      console.log('=== BOOKING DETAILS FETCHED ===');
      console.log('Full booking details:', details);
      console.log('Personal info:', details.personalInfo);
      console.log('Service details:', details.serviceDetails);
      console.log('Service images:', details.serviceImages);
      console.log('Payment method:', details.paymentMethod || details.payment);
      console.log('Booking status:', details.status);
      console.log('Reference code:', details.referenceCode);
      console.log('Additional services:', details.additionalServices);
      console.log('User address:', details.personalInfo?.address);
      console.log('Appointment time from:', details.appointmentTimeFrom);
      console.log('Appointment time to:', details.appointmentTimeTo);
      console.log('Appointment date:', details.appointmentDate);
      console.log('Booking status:', details.bookingStatus);
      console.log('Provider info:', {
        provider: details.provider,
        email: details.email,
        phone: details.phone,
        providerId: details.providerId
      });
      console.log('=== END BOOKING DETAILS ===');

      // Merge the fetched details with existing booking data to ensure all fields are available
      const enrichedDetails = {
        ...booking, // Start with existing booking data
        ...details, // Override with fetched details
        // Ensure critical fields are available
        serviceName: details.serviceName || details.service || booking.service,
        paymentMethod: details.paymentMethod || details.payment || booking.payment,
        serviceImages: details.serviceImages || booking.serviceImages || [],
      };

      setBookingDetails(enrichedDetails);
      setShowBookingDetails(true);

      // Show success message
      toast.success('Booking details loaded successfully!');
    } catch (error) {
      console.error('Error fetching booking details:', error);

      // Provide more specific error messages
      if (error instanceof Error) {
        if (error.message.includes('404')) {
          toast.error('Booking not found. It may have been deleted.');
        } else if (error.message.includes('403')) {
          toast.error('You do not have permission to view this booking.');
        } else if (error.message.includes('500')) {
          toast.error('Server error. Please try again later.');
        } else {
          toast.error(`Failed to load booking details: ${error.message}`);
        }
      } else {
        toast.error('Failed to load booking details. Please try again.');
      }

      // Fallback: show modal with existing booking data if API fails
      console.log('Using fallback booking data:', booking);
      setBookingDetails(booking as BookingType);
      setShowBookingDetails(true);
    } finally {
      setDetailsLoading(false);
    }
  };

  // Function to handle button actions
  const handleButtonAction = (action: string, booking: Booking) => {
    switch (action) {
      case 'View Details':
        handleViewDetails(booking);
        break;
      case 'Confirm':
        handleConfirmBooking(booking);
        break;
      case 'Review':
      case 'Add Review':
        handleAddReview(booking);
        break;
      case 'View Service Reviews':
        handleViewServiceReviews(booking);
        break;
      case 'Chat':
        navigate('/customer/chat');
        break;
      case 'Reschedule':
        handleReschedule(booking);
        break;
      case 'Cancel':
        handleCancelBooking(booking);
        break;
      case 'Rebook':
        // Handle rebook action
        alert('Rebook functionality will be implemented soon');
        break;
      case 'Add to Calendar': {
        // Create calendar event URL (works with Google Calendar)
        const eventTitle = encodeURIComponent(`Appointment: ${booking.service}`);
        const eventDetails = encodeURIComponent(`Provider: ${booking.provider}\nLocation: ${booking.location}\nContact: ${booking.email}, ${booking.phone}`);
        const eventLocation = encodeURIComponent(booking.location);

        // Parse date and time
        const [datePart, timePart] = booking.date.split(',');
        const dateObj = new Date(datePart);

        // Extract start and end times
        let startTime = '', endTime = '';
        if (timePart) {
          const timeRange = timePart.trim();
          const [start, end] = timeRange.split('-');
          startTime = start.trim();
          endTime = end ? end.trim() : '';
        }

        // Create start and end date objects
        const startDate = new Date(dateObj);
        const endDate = new Date(dateObj);

        // Set hours based on time string (simple parsing)
        if (startTime) {
          const [hourMin, period] = startTime.split(' ');
          let hour;
          const minute = hourMin.split(':').map(Number)[1] || 0;
          hour = Number(hourMin.split(':')[0]);
          if (period === 'PM' && hour < 12) hour += 12;
          if (period === 'AM' && hour === 12) hour = 0;
          startDate.setHours(hour, minute, 0);

          // Default end time is 1 hour later if not specified
          if (endTime) {
            const [endHourMin, endPeriod] = endTime.split(' ');
            let endHour;
            const endMinute = endHourMin.split(':').map(Number)[1] || 0;
            endHour = Number(endHourMin.split(':')[0]);
            if (endPeriod === 'PM' && endHour < 12) endHour += 12;
            if (endPeriod === 'AM' && endHour === 12) endHour = 0;
            endDate.setHours(endHour, endMinute, 0);
          } else {
            endDate.setHours(startDate.getHours() + 1, startDate.getMinutes(), 0);
          }
        }

        // Format dates for URL
        const formatDateForCalendar = (date: Date) => {
          return date.toISOString().replace(/-|:|\.\d+/g, '');
        };

        const startDateStr = formatDateForCalendar(startDate);
        const endDateStr = formatDateForCalendar(endDate);

        // Create Google Calendar URL
        const calendarUrl = `https://calendar.google.com/calendar/render?action=TEMPLATE&text=${eventTitle}&details=${eventDetails}&location=${eventLocation}&dates=${startDateStr}/${endDateStr}`;

        // Open in new window
        window.open(calendarUrl, '_blank');
        break;
      }

      default:
        break;
    }
  };

  const getButtonStyles = (action: string): { color: ButtonColor; variant: ButtonVariant } => {
    switch (action) {
      case 'View Details':
        return {
          color: 'default',
          variant: 'bordered'
        };
      case 'Confirm':
        return {
          color: 'success',
          variant: 'solid'
        };
      case 'Review':
        return {
          color: 'primary',
          variant: 'solid'
        };
      case 'Cancel':
        return {
          color: 'danger',
          variant: 'light'
        };
      case 'Chat':
        return {
          color: 'success',
          variant: 'bordered'
        };
      case 'Reschedule':
        return {
          color: 'warning',
          variant: 'bordered'
        };
      case 'Add Review':
        return {
          color: 'secondary',
          variant: 'bordered'
        };
      case 'View Service Reviews':
        return {
          color: 'primary',
          variant: 'light'
        };
      case 'Rebook':
        return {
          color: 'primary',
          variant: 'solid'
        };
      case 'Add to Calendar':
        return {
          color: 'success',
          variant: 'flat'
        };
      default:
        return {
          color: 'primary',
          variant: 'bordered'
        };
    }
  };

  return (
    <div className="mx-auto p-4">
      <BreadCrumb title="My Bookings" item1="Customer" />
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6 mt-4">
        {/* <h2 className="text-3xl font-bold text-gray-800 mb-4 sm:mb-0">My Bookings</h2> */}
        {!bookingsLoading && !bookingsError && bookings.length > 0 && (
          <div className="flex items-center space-x-4 text-sm text-gray-600">
            <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full font-medium">
              Total: {bookings.length}
            </span>
            <span className="bg-green-100 text-green-800 px-3 py-1 rounded-full font-medium">
              Completed: {bookings.filter(b => b.status === 'Completed' || b.status === 'Finished').length}
            </span>
            <span className="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full font-medium">
              Pending: {bookings.filter(b => b.status === 'Pending').length}
            </span>
            <span className="bg-purple-100 text-purple-800 px-3 py-1 rounded-full font-medium">
              Confirmed: {bookings.filter(b => b.status === 'Confirmed').length}
            </span>
          </div>
        )}
      </div>



      {/* Loading State */}
      {bookingsLoading && (
        <div className="flex justify-center items-center py-8">
          <div className="text-lg text-gray-600">Loading bookings...</div>
        </div>
      )}

      {/* Error State */}
      {bookingsError && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <p>{bookingsError}</p>
          <button
            onClick={fetchBookings}
            className="mt-2 bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
          >
            Retry
          </button>
        </div>
      )}

      {/* Bookings List */}
      {!bookingsLoading && !bookingsError && (
        <>
          {bookings.length === 0 ? (
            <div className="text-center py-12">
              <div className="bg-gray-100 rounded-full w-24 h-24 flex items-center justify-center mx-auto mb-4">
                <FaCalendarAlt className="text-gray-400 text-3xl" />
              </div>
              <h3 className="text-xl font-semibold text-gray-600 mb-2">No Bookings Found</h3>
              <p className="text-gray-500 mb-6">You haven't made any bookings yet.</p>
              <button
                onClick={() => window.location.href = '/'}
                className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
              >
                Browse Services
              </button>
            </div>
          ) : (
            <div className="space-y-6">
              {bookings.map((booking) => (
          <div
            key={booking.id}
            className="bg-white p-6 shadow-lg rounded-lg border border-gray-200 hover:shadow-xl transition-shadow duration-300"
          >
            <div className="flex flex-col lg:flex-row lg:items-start gap-6">
              {/* Service Image */}
              <div className="w-full lg:w-32 h-32 flex-shrink-0 rounded-lg overflow-hidden">
                {booking.serviceImages && booking.serviceImages.length > 0 ? (
                  <img
                    src={booking.serviceImages[0]}
                    alt={booking.serviceName || booking.service}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      // Fallback to placeholder if image fails to load
                      (e.target as HTMLImageElement).src = 'https://via.placeholder.com/128x128?text=Service';
                    }}
                  />
                ) : (
                  <div className="w-full h-full bg-gray-300 flex items-center justify-center">
                    <span className="text-gray-500 text-sm text-center">No Image</span>
                  </div>
                )}
              </div>

              {/* Booking Details */}
              <div className="flex-1">
                {/* Header Section */}
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4">
                  <div>
                    <h3 className="text-xl font-bold text-gray-800 mb-2">{booking.serviceName || booking.service}</h3>
                    <div className="flex items-center gap-2 mb-2">
                      <span
                        className={`text-sm px-3 py-1 rounded-full font-medium ${
                          booking.status === 'Cancelled'
                            ? 'bg-red-100 text-red-800'
                            : booking.status === 'Completed'
                              ? 'bg-green-100 text-green-800'
                              : booking.status === 'Confirmed'
                                ? 'bg-blue-100 text-blue-800'
                                : booking.status === 'Pending'
                                  ? 'bg-yellow-100 text-yellow-800'
                                  : booking.status === 'Inprogress'
                                    ? 'bg-purple-100 text-purple-800'
                                    : booking.status === 'Rescheduled'
                                      ? 'bg-amber-100 text-amber-800'
                                      : 'bg-gray-100 text-gray-800'
                        }`}
                      >
                        {booking.status}
                      </span>
                      {booking.bookingId && (
                        <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                          ID: {booking.bookingId}
                        </span>
                      )}
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-2xl font-bold text-green-600">{booking.amount}</p>
                    <p className="text-sm text-gray-500">via {booking.paymentMethod || booking.payment}</p>
                  </div>
                </div>

                {/* Details Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div className="flex items-center space-x-2">
                    <FaCalendarAlt className="text-blue-500 flex-shrink-0" />
                    <div>
                      <p className="text-xs text-gray-500 uppercase tracking-wide">Date & Time</p>
                      <p className="font-medium text-gray-800">{booking.date}</p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <FaMapMarkerAlt className="text-red-500 flex-shrink-0" />
                    <div>
                      <p className="text-xs text-gray-500 uppercase tracking-wide">Location</p>
                      <p className="font-medium text-gray-800">{booking.location}</p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <FaUser className="text-purple-500 flex-shrink-0" />
                    <div>
                      <p className="text-xs text-gray-500 uppercase tracking-wide">Provider</p>
                      <p className="font-medium text-gray-800">{booking.provider}</p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <FaPhone className="text-green-500 flex-shrink-0" />
                    <div>
                      <p className="text-xs text-gray-500 uppercase tracking-wide">Contact</p>
                      <p className="font-medium text-gray-800">{booking.phone}</p>
                    </div>
                  </div>
                </div>

                {/* Additional Details */}
                {(booking.description || booking.duration || booking.category) && (
                  <div className="bg-gray-50 p-3 rounded-lg mb-4">
                    <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 text-sm">
                      {booking.category && (
                        <div>
                          <p className="text-xs text-gray-500 uppercase tracking-wide">Category</p>
                          <p className="font-medium">{booking.category}</p>
                        </div>
                      )}
                      {booking.duration && (
                        <div>
                          <p className="text-xs text-gray-500 uppercase tracking-wide">Duration</p>
                          <p className="font-medium">{booking.duration}</p>
                        </div>
                      )}
                      {booking.serviceId && (
                        <div>
                          <p className="text-xs text-gray-500 uppercase tracking-wide">Service ID</p>
                          <p className="font-medium">{booking.serviceId}</p>
                        </div>
                      )}
                    </div>
                    {booking.description && (
                      <div className="mt-3">
                        <p className="text-xs text-gray-500 uppercase tracking-wide">Description</p>
                        <p className="font-medium text-gray-700">{booking.description}</p>
                      </div>
                    )}
                  </div>
                )}

                {/* Contact Information */}
                <div className="flex items-center justify-between text-sm text-gray-600 mb-4">
                  <div className="flex items-center space-x-4">
                    <a
                      href={`mailto:${booking.email}`}
                      className="flex items-center space-x-1 text-blue-600 hover:text-blue-800 transition-colors"
                    >
                      <FaEnvelope className="text-xs" />
                      <span>{booking.email}</span>
                    </a>
                  </div>
                  {booking.providerId && (
                    <span className="text-xs text-gray-400">Provider ID: {booking.providerId}</span>
                  )}
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="border-t border-gray-200 pt-4 mt-4">
              <div className="flex flex-wrap gap-2 justify-end">
                {booking.actions.map((action, index) => (
                  <CustomButton
                    key={index}
                    label={action}
                    color={getButtonStyles(action).color}
                    variant={getButtonStyles(action).variant}
                    size="sm"
                    radius="md"
                    className="min-w-[100px] font-medium shadow-sm hover:shadow-md transition-shadow"
                    onPress={() => handleButtonAction(action, booking)}
                  />
                ))}
              </div>
            </div>
          </div>
              ))}
            </div>
          )}
        </>
      )}

      {/* Review Form Modal */}
      {selectedBooking && showReviewForm && (
        <ReviewForm
          isOpen={showReviewForm}
          onClose={() => setShowReviewForm(false)}
          onSubmit={handleSubmitReview}
          providerId={selectedBooking.providerId || `provider-${selectedBooking.id}`}
          serviceId={selectedBooking.serviceId || selectedBooking.id.toString()}
          bookingId={selectedBooking.bookingId || `booking-${selectedBooking.id}`}
          serviceName={selectedBooking.serviceName || selectedBooking.service}
          initialData={{
            id: `review-${Date.now()}`,
            providerId: selectedBooking.providerId || `provider-${selectedBooking.id}`,
            serviceId: selectedBooking.serviceId || selectedBooking.id.toString(),
            serviceName: selectedBooking.serviceName || selectedBooking.service,
            rating: 0,
            title: `Review for ${selectedBooking.serviceName || selectedBooking.service}`,
            review: '',
            images: [],
            imageUrls: [],
            imageNames: [] // Include empty image names array
          }}
        />
      )}

      {/* Reschedule Form Modal */}
      {selectedBooking && showRescheduleForm && (
        <RescheduleForm
          isOpen={showRescheduleForm}
          onClose={() => setShowRescheduleForm(false)}
          onSubmit={handleRescheduleSubmit}
          bookingDetails={{
            id: typeof selectedBooking.id === 'string' ? parseInt(selectedBooking.id) : selectedBooking.id,
            service: selectedBooking.service,
            date: selectedBooking.date,
            provider: selectedBooking.provider
          }}
        />
      )}

      {/* Booking Details Modal */}
      <Modal
        isOpen={showBookingDetails}
        onClose={() => {
          setShowBookingDetails(false);
          setBookingDetails(null);
          setSelectedBooking(null);
        }}
        size="2xl"
        scrollBehavior="inside"
      >
        <ModalContent>
          <ModalHeader className="flex flex-row items-center justify-between">
            <div className="flex flex-col gap-1">
              <h2 className="text-xl font-bold">Booking Details</h2>
              {selectedBooking && (
                <p className="text-sm text-gray-600">Booking ID: {selectedBooking.bookingId || selectedBooking.id}</p>
              )}
            </div>
            <div className="flex items-center gap-2">
              <Button
                size="sm"
                variant="light"
                color="primary"
                onPress={() => selectedBooking && handleViewDetails(selectedBooking)}
                isLoading={detailsLoading}
                className="min-w-unit-16"
              >
                🔄 Refresh
              </Button>
            </div>
          </ModalHeader>

          <ModalBody>
            {detailsLoading ? (
              <div className="flex justify-center items-center py-8">
                <Spinner size="lg" />
                <span className="ml-2">Loading booking details...</span>
              </div>
            ) : bookingDetails ? (
              <div className="space-y-6">
                {/* Booking Summary Card */}
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-lg border border-blue-200">
                  <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                    <div className="flex-1">
                      <h3 className="text-2xl font-bold text-gray-800 mb-2">
                        {bookingDetails.serviceName || bookingDetails.service}
                      </h3>
                      <div className="flex flex-col gap-3 mb-3">
                        {/* Primary Status Display */}
                        <div className="flex items-center gap-3">
                          <Chip
                            color={
                              bookingDetails.status === 'Confirmed' ? 'success' :
                              bookingDetails.status === 'Pending' ? 'warning' :
                              bookingDetails.status === 'Cancelled' ? 'danger' :
                              bookingDetails.status === 'Finished' ? 'primary' :
                              bookingDetails.status === 'Completed' ? 'success' :
                              bookingDetails.status === 'Inprogress' ? 'secondary' :
                              'default'
                            }
                            variant="flat"
                            size="lg"
                          >
                            {bookingDetails.status}
                          </Chip>
                          <span className="text-sm text-gray-600 bg-white px-3 py-1 rounded-full">
                            ID: {bookingDetails.bookingId || bookingDetails.id}
                          </span>
                          {bookingDetails.referenceCode && (
                            <span className="text-sm text-purple-600 bg-purple-100 px-3 py-1 rounded-full font-medium">
                              Ref: {bookingDetails.referenceCode}
                            </span>
                          )}
                        </div>

                        {/* Detailed Booking Status */}
                        {bookingDetails.bookingStatus && (
                          <div className="bg-white p-3 rounded-lg border border-gray-200">
                            <div className="flex items-center space-x-2">
                              <span className="text-blue-500">📋</span>
                              <div>
                                <p className="text-xs text-gray-500 uppercase tracking-wide">Detailed Status</p>
                                <p className="font-semibold text-gray-800">{bookingDetails.bookingStatus}</p>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 text-sm">
                        <div className="flex items-center space-x-2">
                          <FaCalendarAlt className="text-blue-500" />
                          <div>
                            <span className="font-medium">{bookingDetails.appointmentDate || bookingDetails.date}</span>
                            {(bookingDetails.appointmentTimeFrom || bookingDetails.appointmentTimeTo) && (
                              <div className="text-xs text-gray-600 mt-1">
                                {bookingDetails.appointmentTimeFrom && bookingDetails.appointmentTimeTo ? (
                                  <span>🕐 {bookingDetails.appointmentTimeFrom} - {bookingDetails.appointmentTimeTo}</span>
                                ) : bookingDetails.appointmentTimeFrom ? (
                                  <span>🕐 From: {bookingDetails.appointmentTimeFrom}</span>
                                ) : bookingDetails.appointmentTimeTo ? (
                                  <span>🕐 Until: {bookingDetails.appointmentTimeTo}</span>
                                ) : null}
                              </div>
                            )}
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <FaMapMarkerAlt className="text-red-500" />
                          <span className="font-medium">{bookingDetails.location}</span>
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-3xl font-bold text-green-600 mb-1">
                        {bookingDetails.amount}
                      </div>
                      <div className="text-sm text-gray-600">
                        via {bookingDetails.paymentMethod || bookingDetails.payment}
                      </div>
                      {bookingDetails.createdAt && (
                        <div className="text-xs text-gray-500 mt-2">
                          Booked: {new Date(bookingDetails.createdAt).toLocaleDateString()}
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Service Images */}
                {bookingDetails.serviceImages && bookingDetails.serviceImages.length > 0 && (
                  <div>
                    <h4 className="text-lg font-semibold mb-3 flex items-center">
                      <span className="bg-purple-100 text-purple-800 p-2 rounded-lg mr-3">
                        🖼️
                      </span>
                      Service Images
                    </h4>
                    <div className="flex gap-3 overflow-x-auto pb-2">
                      {bookingDetails.serviceImages.map((image, index) => (
                        <div key={index} className="flex-shrink-0">
                          <img
                            src={image}
                            alt={`Service ${index + 1}`}
                            className="w-24 h-24 object-cover rounded-lg shadow-md hover:shadow-lg transition-shadow cursor-pointer"
                            onError={(e) => {
                              (e.target as HTMLImageElement).style.display = 'none';
                            }}
                            onClick={() => window.open(image, '_blank')}
                          />
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Appointment Time Details */}
                {(bookingDetails.appointmentDate || bookingDetails.appointmentTimeFrom || bookingDetails.appointmentTimeTo) && (
                  <div>
                    <h4 className="text-lg font-semibold mb-3 flex items-center">
                      <span className="bg-blue-100 text-blue-800 p-2 rounded-lg mr-3">
                        🕐
                      </span>
                      Appointment Schedule
                    </h4>
                    <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        {/* Appointment Date */}
                        {bookingDetails.appointmentDate && (
                          <div className="flex items-center space-x-3">
                            <FaCalendarAlt className="text-blue-600 text-xl" />
                            <div>
                              <p className="text-sm text-blue-600 font-semibold mb-1">Appointment Date</p>
                              <p className="font-bold text-blue-800">{bookingDetails.appointmentDate}</p>
                            </div>
                          </div>
                        )}

                        {/* Start Time */}
                        {bookingDetails.appointmentTimeFrom && (
                          <div className="flex items-center space-x-3">
                            <div className="bg-green-100 p-2 rounded-full">
                              <span className="text-green-600 font-bold">▶️</span>
                            </div>
                            <div>
                              <p className="text-sm text-green-600 font-semibold mb-1">Start Time</p>
                              <p className="font-bold text-green-800">{bookingDetails.appointmentTimeFrom}</p>
                            </div>
                          </div>
                        )}

                        {/* End Time */}
                        {bookingDetails.appointmentTimeTo && (
                          <div className="flex items-center space-x-3">
                            <div className="bg-red-100 p-2 rounded-full">
                              <span className="text-red-600 font-bold">⏹️</span>
                            </div>
                            <div>
                              <p className="text-sm text-red-600 font-semibold mb-1">End Time</p>
                              <p className="font-bold text-red-800">{bookingDetails.appointmentTimeTo}</p>
                            </div>
                          </div>
                        )}
                      </div>

                      {/* Duration Calculation */}
                      {bookingDetails.appointmentTimeFrom && bookingDetails.appointmentTimeTo && (
                        <div className="mt-4 pt-4 border-t border-blue-200">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-2">
                              <span className="text-blue-600">⏱️</span>
                              <span className="text-sm text-blue-600 font-semibold">Duration:</span>
                            </div>
                            <span className="font-bold text-blue-800">
                              {bookingDetails.appointmentTimeFrom} - {bookingDetails.appointmentTimeTo}
                            </span>
                          </div>
                        </div>
                      )}

                      {/* Fallback to regular date if appointment fields not available */}
                      {!bookingDetails.appointmentDate && bookingDetails.date && (
                        <div className="mt-4 pt-4 border-t border-blue-200">
                          <div className="flex items-center space-x-2">
                            <FaCalendarAlt className="text-blue-500" />
                            <div>
                              <p className="text-sm text-blue-600 font-semibold mb-1">Booking Date & Time</p>
                              <p className="font-medium text-blue-800">{bookingDetails.date}</p>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Booking Status Details */}
                {bookingDetails.bookingStatus && (
                  <div>
                    <h4 className="text-lg font-semibold mb-3 flex items-center">
                      <span className="bg-orange-100 text-orange-800 p-2 rounded-lg mr-3">
                        📊
                      </span>
                      Booking Status Details
                    </h4>
                    <div className="bg-orange-50 p-4 rounded-lg border border-orange-200">
                      <div className="flex items-start space-x-4">
                        {/* Status Icon */}
                        <div className="flex-shrink-0">
                          <div className={`p-3 rounded-full ${
                            bookingDetails.status === 'Confirmed' ? 'bg-green-100' :
                            bookingDetails.status === 'Pending' ? 'bg-yellow-100' :
                            bookingDetails.status === 'Cancelled' ? 'bg-red-100' :
                            bookingDetails.status === 'Finished' ? 'bg-blue-100' :
                            bookingDetails.status === 'Completed' ? 'bg-green-100' :
                            bookingDetails.status === 'Inprogress' ? 'bg-purple-100' :
                            'bg-gray-100'
                          }`}>
                            <span className="text-2xl">
                              {bookingDetails.status === 'Confirmed' ? '✅' :
                               bookingDetails.status === 'Pending' ? '⏳' :
                               bookingDetails.status === 'Cancelled' ? '❌' :
                               bookingDetails.status === 'Finished' ? '🏁' :
                               bookingDetails.status === 'Completed' ? '✅' :
                               bookingDetails.status === 'Inprogress' ? '🔄' :
                               '📋'}
                            </span>
                          </div>
                        </div>

                        {/* Status Information */}
                        <div className="flex-1">
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                              <p className="text-sm text-orange-600 font-semibold mb-1">Current Status</p>
                              <p className="font-bold text-orange-800 text-lg">{bookingDetails.status}</p>
                            </div>
                            <div>
                              <p className="text-sm text-orange-600 font-semibold mb-1">Status Details</p>
                              <p className="font-medium text-orange-800">{bookingDetails.bookingStatus}</p>
                            </div>
                          </div>

                          {/* Status Timeline */}
                          <div className="mt-4 pt-4 border-t border-orange-200">
                            <p className="text-sm text-orange-600 font-semibold mb-2">Status Information</p>
                            <div className="bg-white p-3 rounded-lg">
                              <p className="text-gray-800">{bookingDetails.bookingStatus}</p>
                            </div>
                          </div>

                          {/* Status Actions */}
                          <div className="mt-4 pt-4 border-t border-orange-200">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center space-x-2">
                                <span className="text-orange-600">🔄</span>
                                <span className="text-sm text-orange-600 font-semibold">Status Updates:</span>
                              </div>
                              <div className="flex items-center space-x-2">
                                <Chip
                                  color={
                                    bookingDetails.status === 'Confirmed' ? 'success' :
                                    bookingDetails.status === 'Pending' ? 'warning' :
                                    bookingDetails.status === 'Cancelled' ? 'danger' :
                                    bookingDetails.status === 'Finished' ? 'primary' :
                                    bookingDetails.status === 'Completed' ? 'success' :
                                    bookingDetails.status === 'Inprogress' ? 'secondary' :
                                    'default'
                                  }
                                  variant="solid"
                                  size="sm"
                                >
                                  {bookingDetails.status}
                                </Chip>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Status and Service Info */}
                <div className="bg-gray-50 p-4 rounded-lg">
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="text-lg font-semibold">{bookingDetails.serviceName || bookingDetails.service}</h3>
                    <Chip
                      color={
                        bookingDetails.status === 'Confirmed' ? 'success' :
                        bookingDetails.status === 'Pending' ? 'warning' :
                        bookingDetails.status === 'Cancelled' ? 'danger' :
                        bookingDetails.status === 'Finished' ? 'primary' :
                        'default'
                      }
                      variant="flat"
                    >
                      {bookingDetails.status}
                    </Chip>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex items-center space-x-2">
                      <FaCalendarAlt className="text-blue-500" />
                      <div>
                        <p className="text-sm text-gray-600 mb-1">Date & Time</p>
                        <p className="font-medium">{bookingDetails.date}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <FaDollarSign className="text-green-500" />
                      <div>
                        <p className="text-sm text-gray-600 mb-1">Amount</p>
                        <p className="font-medium">{bookingDetails.amount}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <FaDollarSign className="text-purple-500" />
                      <div>
                        <p className="text-sm text-gray-600 mb-1">Payment Method</p>
                        <p className="font-medium">{bookingDetails.paymentMethod || bookingDetails.payment}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <FaMapMarkerAlt className="text-red-500" />
                      <div>
                        <p className="text-sm text-gray-600 mb-1">Location</p>
                        <p className="font-medium">{bookingDetails.location}</p>
                      </div>
                    </div>
                  </div>
                </div>

                <Divider />

                {/* Provider Information */}
                <div>
                  <h4 className="text-md font-semibold mb-3">Provider Information</h4>
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="flex items-center space-x-2">
                        <FaUser className="text-blue-500" />
                        <div>
                          <p className="text-sm text-gray-600 mb-1">Provider Name</p>
                          <p className="font-medium">{bookingDetails.provider}</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <FaEnvelope className="text-green-500" />
                        <div>
                          <p className="text-sm text-gray-600 mb-1">Email</p>
                          <p className="font-medium">{bookingDetails.email}</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <FaPhone className="text-purple-500" />
                        <div>
                          <p className="text-sm text-gray-600 mb-1">Phone</p>
                          <p className="font-medium">{bookingDetails.phone}</p>
                        </div>
                      </div>
                      {bookingDetails.providerId && (
                        <div className="flex items-center space-x-2">
                          <FaEye className="text-gray-500" />
                          <div>
                            <p className="text-sm text-gray-600 mb-1">Provider ID</p>
                            <p className="font-medium">{bookingDetails.providerId}</p>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Additional Service Information */}
                {(bookingDetails.description || bookingDetails.duration || bookingDetails.category) && (
                  <>
                    <Divider />
                    <div>
                      <h4 className="text-md font-semibold mb-3">Service Details</h4>
                      <div className="bg-green-50 p-4 rounded-lg">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {bookingDetails.description && (
                            <div className="md:col-span-2">
                              <p className="text-sm text-gray-600 mb-1">Description</p>
                              <p className="font-medium">{bookingDetails.description}</p>
                            </div>
                          )}
                          {bookingDetails.duration && (
                            <div>
                              <p className="text-sm text-gray-600 mb-1">Duration</p>
                              <p className="font-medium">{bookingDetails.duration}</p>
                            </div>
                          )}
                          {bookingDetails.category && (
                            <div>
                              <p className="text-sm text-gray-600 mb-1">Category</p>
                              <p className="font-medium">{bookingDetails.category}</p>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </>
                )}

                {/* Personal Information */}
                {bookingDetails.personalInfo && (
                  <>
                    <Divider />
                    <div>
                      <h4 className="text-md font-semibold mb-3">Personal Information</h4>
                      <div className="bg-blue-50 p-4 rounded-lg">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {(bookingDetails.personalInfo.firstName || bookingDetails.personalInfo.lastName) && (
                            <div className="flex items-center space-x-2">
                              <FaUser className="text-blue-500" />
                              <div>
                                <p className="text-sm text-gray-600 mb-1">Full Name</p>
                                <p className="font-medium">
                                  {bookingDetails.personalInfo.firstName} {bookingDetails.personalInfo.lastName}
                                </p>
                              </div>
                            </div>
                          )}
                          {bookingDetails.personalInfo.email && (
                            <div className="flex items-center space-x-2">
                              <FaEnvelope className="text-green-500" />
                              <div>
                                <p className="text-sm text-gray-600 mb-1">Email</p>
                                <p className="font-medium">{bookingDetails.personalInfo.email}</p>
                              </div>
                            </div>
                          )}
                          {bookingDetails.personalInfo.phone && (
                            <div className="flex items-center space-x-2">
                              <FaPhone className="text-purple-500" />
                              <div>
                                <p className="text-sm text-gray-600 mb-1">Phone</p>
                                <p className="font-medium">{bookingDetails.personalInfo.phone}</p>
                              </div>
                            </div>
                          )}
                          {/* Enhanced Address Display */}
                          {(bookingDetails.personalInfo.streetAddress || bookingDetails.personalInfo.address) && (
                            <div className="md:col-span-2">
                              <div className="bg-white p-4 rounded-lg border border-gray-200">
                                <div className="flex items-start space-x-3">
                                  <FaHome className="text-red-500 mt-1 flex-shrink-0" />
                                  <div className="flex-1">
                                    <p className="text-sm text-gray-600 mb-2 font-semibold">Complete Address</p>

                                    {/* Primary Address (from personalInfo) */}
                                    {bookingDetails.personalInfo.streetAddress && (
                                      <div className="mb-3">
                                        <p className="text-xs text-gray-500 mb-1">Primary Address:</p>
                                        <p className="font-medium text-gray-800">
                                          {bookingDetails.personalInfo.streetAddress}
                                          {bookingDetails.personalInfo.city && `, ${bookingDetails.personalInfo.city}`}
                                          {bookingDetails.personalInfo.state && `, ${bookingDetails.personalInfo.state}`}
                                          {bookingDetails.personalInfo.postalCode && ` ${bookingDetails.personalInfo.postalCode}`}
                                        </p>
                                      </div>
                                    )}

                                    {/* Structured Address (from address object) */}
                                    {bookingDetails.personalInfo.address && (
                                      <div className="mb-3">
                                        <p className="text-xs text-gray-500 mb-1">Structured Address:</p>
                                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 text-sm">
                                          {bookingDetails.personalInfo.address.street && (
                                            <div>
                                              <span className="text-gray-600">Street:</span>
                                              <span className="font-medium ml-1">{bookingDetails.personalInfo.address.street}</span>
                                            </div>
                                          )}
                                          {bookingDetails.personalInfo.address.city && (
                                            <div>
                                              <span className="text-gray-600">City:</span>
                                              <span className="font-medium ml-1">{bookingDetails.personalInfo.address.city}</span>
                                            </div>
                                          )}
                                          {bookingDetails.personalInfo.address.state && (
                                            <div>
                                              <span className="text-gray-600">State:</span>
                                              <span className="font-medium ml-1">{bookingDetails.personalInfo.address.state}</span>
                                            </div>
                                          )}
                                          {bookingDetails.personalInfo.address.postalCode && (
                                            <div>
                                              <span className="text-gray-600">Postal Code:</span>
                                              <span className="font-medium ml-1">{bookingDetails.personalInfo.address.postalCode}</span>
                                            </div>
                                          )}
                                          {bookingDetails.personalInfo.address.country && (
                                            <div>
                                              <span className="text-gray-600">Country:</span>
                                              <span className="font-medium ml-1">{bookingDetails.personalInfo.address.country}</span>
                                            </div>
                                          )}
                                        </div>
                                      </div>
                                    )}

                                    {/* Complete Address String */}
                                    <div className="bg-gray-50 p-2 rounded text-sm">
                                      <span className="text-gray-600">Full Address:</span>
                                      <p className="font-medium mt-1">
                                        {bookingDetails.personalInfo.address?.street || bookingDetails.personalInfo.streetAddress}
                                        {(bookingDetails.personalInfo.address?.city || bookingDetails.personalInfo.city) &&
                                          `, ${bookingDetails.personalInfo.address?.city || bookingDetails.personalInfo.city}`}
                                        {(bookingDetails.personalInfo.address?.state || bookingDetails.personalInfo.state) &&
                                          `, ${bookingDetails.personalInfo.address?.state || bookingDetails.personalInfo.state}`}
                                        {(bookingDetails.personalInfo.address?.postalCode || bookingDetails.personalInfo.postalCode) &&
                                          ` ${bookingDetails.personalInfo.address?.postalCode || bookingDetails.personalInfo.postalCode}`}
                                        {bookingDetails.personalInfo.address?.country &&
                                          `, ${bookingDetails.personalInfo.address.country}`}
                                      </p>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          )}
                          {bookingDetails.personalInfo.notes && (
                            <div className="md:col-span-2 flex items-start space-x-2">
                              <FaStickyNote className="text-yellow-500 mt-1" />
                              <div>
                                <p className="text-sm text-gray-600 mb-1">Notes</p>
                                <p className="font-medium">{bookingDetails.personalInfo.notes}</p>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </>
                )}

                {/* Service Details */}
                {bookingDetails.serviceDetails && (
                  <>
                    <Divider />
                    <div>
                      <h4 className="text-md font-semibold mb-3">Service Information</h4>
                      <div className="bg-purple-50 p-4 rounded-lg">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {bookingDetails.serviceDetails.serviceTitle && (
                            <div className="flex items-center space-x-2">
                              <FaCog className="text-purple-500" />
                              <div>
                                <p className="text-sm text-gray-600 mb-1">Service Title</p>
                                <p className="font-medium">{bookingDetails.serviceDetails.serviceTitle}</p>
                              </div>
                            </div>
                          )}
                          {bookingDetails.serviceDetails.price && (
                            <div className="flex items-center space-x-2">
                              <FaDollarSign className="text-green-500" />
                              <div>
                                <p className="text-sm text-gray-600 mb-1">Base Price</p>
                                <p className="font-medium">${bookingDetails.serviceDetails.price}</p>
                              </div>
                            </div>
                          )}
                          {bookingDetails.serviceDetails.categoryId && (
                            <div className="flex items-center space-x-2">
                              <FaTag className="text-blue-500" />
                              <div>
                                <p className="text-sm text-gray-600 mb-1">Category ID</p>
                                <p className="font-medium">{bookingDetails.serviceDetails.categoryId}</p>
                              </div>
                            </div>
                          )}
                          {bookingDetails.serviceDetails.subCategoryId && (
                            <div className="flex items-center space-x-2">
                              <FaTag className="text-indigo-500" />
                              <div>
                                <p className="text-sm text-gray-600 mb-1">Sub Category ID</p>
                                <p className="font-medium">{bookingDetails.serviceDetails.subCategoryId}</p>
                              </div>
                            </div>
                          )}
                          {bookingDetails.serviceDetails.isOffers !== undefined && (
                            <div className="flex items-center space-x-2">
                              <FaTag className="text-orange-500" />
                              <div>
                                <p className="text-sm text-gray-600 mb-1">Has Offers</p>
                                <p className="font-medium">{bookingDetails.serviceDetails.isOffers ? 'Yes' : 'No'}</p>
                              </div>
                            </div>
                          )}
                          {bookingDetails.serviceDetails.isAdditional !== undefined && (
                            <div className="flex items-center space-x-2">
                              <FaCog className="text-gray-500" />
                              <div>
                                <p className="text-sm text-gray-600 mb-1">Additional Services</p>
                                <p className="font-medium">{bookingDetails.serviceDetails.isAdditional ? 'Available' : 'Not Available'}</p>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </>
                )}

                {/* Additional Services */}
                {bookingDetails.additionalServices && bookingDetails.additionalServices.length > 0 && (
                  <>
                    <Divider />
                    <div>
                      <h4 className="text-lg font-semibold mb-3 flex items-center">
                        <span className="bg-green-100 text-green-800 p-2 rounded-lg mr-3">
                          ➕
                        </span>
                        Additional Services ({bookingDetails.additionalServices.length})
                      </h4>
                      <div className="space-y-4">
                        {bookingDetails.additionalServices.map((service, index) => (
                          <div key={service.id || index} className="bg-green-50 p-4 rounded-lg border border-green-200">
                            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-3">
                              <div>
                                <h5 className="font-semibold text-green-800 text-lg">
                                  {service.name || `Additional Service ${index + 1}`}
                                </h5>
                                {service.id && (
                                  <p className="text-xs text-green-600 mt-1">ID: {service.id}</p>
                                )}
                              </div>
                              {service.price && (
                                <div className="text-right">
                                  <p className="text-xl font-bold text-green-600">${service.price}</p>
                                  {service.duration && (
                                    <p className="text-sm text-green-600">{service.duration}</p>
                                  )}
                                </div>
                              )}
                            </div>

                            {service.description && (
                              <div className="mb-3">
                                <p className="text-sm text-gray-600 mb-1">Description:</p>
                                <p className="text-gray-800">{service.description}</p>
                              </div>
                            )}

                            <div className="grid grid-cols-2 sm:grid-cols-4 gap-3 text-sm">
                              {service.price && (
                                <div className="flex items-center space-x-2">
                                  <FaDollarSign className="text-green-500" />
                                  <div>
                                    <p className="text-xs text-gray-500">Price</p>
                                    <p className="font-medium">${service.price}</p>
                                  </div>
                                </div>
                              )}
                              {service.duration && (
                                <div className="flex items-center space-x-2">
                                  <FaCalendarAlt className="text-blue-500" />
                                  <div>
                                    <p className="text-xs text-gray-500">Duration</p>
                                    <p className="font-medium">{service.duration}</p>
                                  </div>
                                </div>
                              )}
                              {service.id && (
                                <div className="flex items-center space-x-2">
                                  <FaTag className="text-purple-500" />
                                  <div>
                                    <p className="text-xs text-gray-500">Service ID</p>
                                    <p className="font-medium">{service.id}</p>
                                  </div>
                                </div>
                              )}
                              <div className="flex items-center space-x-2">
                                <FaCog className="text-gray-500" />
                                <div>
                                  <p className="text-xs text-gray-500">Type</p>
                                  <p className="font-medium">Additional</p>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}

                        {/* Additional Services Summary */}
                        <div className="bg-green-100 p-3 rounded-lg">
                          <div className="flex items-center justify-between">
                            <span className="font-semibold text-green-800">
                              Total Additional Services: {bookingDetails.additionalServices.length}
                            </span>
                            {bookingDetails.additionalServices.some(s => s.price) && (
                              <span className="font-bold text-green-800">
                                Total Cost: ${bookingDetails.additionalServices
                                  .filter(s => s.price)
                                  .reduce((sum, s) => sum + (s.price || 0), 0)
                                  .toFixed(2)}
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </>
                )}

                <Divider />

                {/* Booking Metadata */}
                <div>
                  <h4 className="text-md font-semibold mb-3">Booking Information</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {bookingDetails.referenceCode && (
                      <div className="md:col-span-2 bg-purple-50 p-3 rounded-lg border border-purple-200">
                        <div className="flex items-center space-x-2">
                          <FaTag className="text-purple-500" />
                          <div>
                            <p className="text-sm text-purple-600 mb-1 font-semibold">Reference Code</p>
                            <p className="font-bold text-purple-800 text-lg">{bookingDetails.referenceCode}</p>
                            <p className="text-xs text-purple-600 mt-1">Use this code for customer support inquiries</p>
                          </div>
                        </div>
                      </div>
                    )}
                    {bookingDetails.bookingStatus && (
                      <div className="md:col-span-2 bg-orange-50 p-3 rounded-lg border border-orange-200">
                        <div className="flex items-center space-x-2">
                          <span className="text-orange-500">📊</span>
                          <div>
                            <p className="text-sm text-orange-600 mb-1 font-semibold">Booking Status Details</p>
                            <p className="font-medium text-orange-800">{bookingDetails.bookingStatus}</p>
                            <p className="text-xs text-orange-600 mt-1">Current booking status information</p>
                          </div>
                        </div>
                      </div>
                    )}
                    {bookingDetails.serviceId && (
                      <div>
                        <p className="text-sm text-gray-600 mb-1">Service ID</p>
                        <p className="font-medium">{bookingDetails.serviceId}</p>
                      </div>
                    )}
                    {bookingDetails.userId && (
                      <div>
                        <p className="text-sm text-gray-600 mb-1">User ID</p>
                        <p className="font-medium">{bookingDetails.userId}</p>
                      </div>
                    )}
                    {bookingDetails.createdAt && (
                      <div>
                        <p className="text-sm text-gray-600 mb-1">Created At</p>
                        <p className="font-medium">{new Date(bookingDetails.createdAt).toLocaleString()}</p>
                      </div>
                    )}
                    {bookingDetails.updatedAt && (
                      <div>
                        <p className="text-sm text-gray-600 mb-1">Last Updated</p>
                        <p className="font-medium">{new Date(bookingDetails.updatedAt).toLocaleString()}</p>
                      </div>
                    )}
                  </div>
                </div>

                

                {/* Complete Booking Data Summary */}
                <div>
                  <h4 className="text-md font-semibold mb-3 flex items-center">
                    <span className="bg-indigo-100 text-indigo-800 p-2 rounded-lg mr-3">
                      📋
                    </span>
                    Complete Booking Summary
                  </h4>
                  <div className="bg-indigo-50 p-4 rounded-lg">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                      <div className="space-y-2">
                        <h5 className="font-semibold text-indigo-800">Basic Info</h5>
                        <div className="space-y-1">
                          <p><span className="font-medium">Booking ID:</span> {bookingDetails.bookingId || bookingDetails.id}</p>
                          <p><span className="font-medium">Service:</span> {bookingDetails.serviceName || bookingDetails.service}</p>
                          <p><span className="font-medium">Status:</span> {bookingDetails.status}</p>
                          <p><span className="font-medium">Amount:</span> {bookingDetails.amount}</p>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <h5 className="font-semibold text-indigo-800">Provider Info</h5>
                        <div className="space-y-1">
                          <p><span className="font-medium">Provider:</span> {bookingDetails.provider}</p>
                          <p><span className="font-medium">Email:</span> {bookingDetails.email}</p>
                          <p><span className="font-medium">Phone:</span> {bookingDetails.phone}</p>
                          <p><span className="font-medium">Provider ID:</span> {bookingDetails.providerId || 'N/A'}</p>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <h5 className="font-semibold text-indigo-800">Additional Data</h5>
                        <div className="space-y-1">
                          <p><span className="font-medium">Service ID:</span> {bookingDetails.serviceId || 'N/A'}</p>
                          <p><span className="font-medium">User ID:</span> {bookingDetails.userId || 'N/A'}</p>
                          <p><span className="font-medium">Payment:</span> {bookingDetails.paymentMethod || bookingDetails.payment}</p>
                          <p><span className="font-medium">Images:</span> {bookingDetails.serviceImages?.length || 0} image(s)</p>
                          <p><span className="font-medium">Reference Code:</span> {bookingDetails.referenceCode || 'N/A'}</p>
                          <p><span className="font-medium">Additional Services:</span> {bookingDetails.additionalServices?.length || 0} service(s)</p>
                          <p><span className="font-medium">Booking Status:</span> {bookingDetails.bookingStatus || 'N/A'}</p>
                          <p><span className="font-medium">Appointment Times:</span> {bookingDetails.appointmentTimeFrom && bookingDetails.appointmentTimeTo ? `${bookingDetails.appointmentTimeFrom} - ${bookingDetails.appointmentTimeTo}` : 'N/A'}</p>
                        </div>
                      </div>
                    </div>

                    {/* Data Availability Indicators */}
                    <div className="mt-4 pt-4 border-t border-indigo-200">
                      <h5 className="font-semibold text-indigo-800 mb-2">Data Availability</h5>
                      <div className="flex flex-wrap gap-2">
                        <span className={`px-2 py-1 rounded-full text-xs ${bookingDetails.personalInfo ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                          {bookingDetails.personalInfo ? '✅' : '❌'} Personal Info
                        </span>
                        <span className={`px-2 py-1 rounded-full text-xs ${bookingDetails.serviceDetails ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                          {bookingDetails.serviceDetails ? '✅' : '❌'} Service Details
                        </span>
                        <span className={`px-2 py-1 rounded-full text-xs ${bookingDetails.serviceImages?.length ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                          {bookingDetails.serviceImages?.length ? '✅' : '❌'} Service Images
                        </span>
                        <span className={`px-2 py-1 rounded-full text-xs ${bookingDetails.description ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                          {bookingDetails.description ? '✅' : '❌'} Description
                        </span>
                        <span className={`px-2 py-1 rounded-full text-xs ${bookingDetails.referenceCode ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                          {bookingDetails.referenceCode ? '✅' : '❌'} Reference Code
                        </span>
                        <span className={`px-2 py-1 rounded-full text-xs ${bookingDetails.additionalServices?.length ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                          {bookingDetails.additionalServices?.length ? '✅' : '❌'} Additional Services
                        </span>
                        <span className={`px-2 py-1 rounded-full text-xs ${(bookingDetails.personalInfo?.address || bookingDetails.personalInfo?.streetAddress) ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                          {(bookingDetails.personalInfo?.address || bookingDetails.personalInfo?.streetAddress) ? '✅' : '❌'} User Address
                        </span>
                        <span className={`px-2 py-1 rounded-full text-xs ${bookingDetails.bookingStatus ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                          {bookingDetails.bookingStatus ? '✅' : '❌'} Booking Status
                        </span>
                        <span className={`px-2 py-1 rounded-full text-xs ${(bookingDetails.appointmentTimeFrom || bookingDetails.appointmentTimeTo) ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                          {(bookingDetails.appointmentTimeFrom || bookingDetails.appointmentTimeTo) ? '✅' : '❌'} Appointment Times
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Available Actions */}
                {selectedBooking && selectedBooking.actions.length > 1 && (
                  <>
                    <Divider />
                    <div>
                      <h4 className="text-md font-semibold mb-3">Available Actions</h4>
                      <div className="flex flex-wrap gap-2">
                        {selectedBooking.actions
                          .filter(action => action !== 'View Details')
                          .map((action, index) => {
                            const styles = getButtonStyles(action);
                            return (
                              <Button
                                key={index}
                                color={styles.color}
                                variant={styles.variant}
                                size="sm"
                                onPress={() => {
                                  setShowBookingDetails(false);
                                  handleButtonAction(action, selectedBooking);
                                }}
                              >
                                {action}
                              </Button>
                            );
                          })}
                      </div>
                    </div>
                  </>
                )}
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-gray-600">Failed to load booking details.</p>
              </div>
            )}
          </ModalBody>

          <ModalFooter>
            <Button
              color="default"
              variant="light"
              onPress={() => {
                setShowBookingDetails(false);
                setBookingDetails(null);
                setSelectedBooking(null);
              }}
            >
              Close
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
};

export default BookingList;
