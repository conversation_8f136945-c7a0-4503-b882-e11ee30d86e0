"use client"

import type React from "react"
import { useState } from "react"
import { Avatar, Button, Card, CardBody, CardHeader, Textarea, Input, Chip, Divider } from "@heroui/react"
import { Star, MessageCircle, Send } from "lucide-react"
import type { Review, Reply } from "../service-review"

interface ReviewCardProps {
  review: Review
  onReply: (reviewId: string, reply: Omit<Reply, "id">) => void
}

export function ReviewCard({ review, onReply }: ReviewCardProps) {
  const [showReplyForm, setShowReplyForm] = useState(false)
  const [replyContent, setReplyContent] = useState("")
  const [replyAuthor, setReplyAuthor] = useState("")

  const handleReplySubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (!replyContent.trim() || !replyAuthor.trim()) return

    onReply(review.id, {
      authorName: replyAuthor.trim(),
      content: replyContent.trim(),
      date: new Date().toISOString().split("T")[0],
      isBusinessOwner: false,
    })

    setReplyContent("")
    setReplyAuthor("")
    setShowReplyForm(false)
  }

  return (
    <Card>
      <CardHeader className="pb-4">
        <div className="flex items-start gap-4">
          <Avatar
            src={review.customerAvatar || "/placeholder.svg"}
            name={review.customerName}
            size="md"
          />
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-1">
              <h3 className="font-semibold">{review.customerName}</h3>
              <span className="text-sm text-gray-500">{review.date}</span>
            </div>
            <div className="flex items-center gap-2 mb-2">
              <div className="flex">
                {[1, 2, 3, 4].map((star) => (
                  <Star
                    key={star}
                    className={`w-4 h-4 ${
                      star <= review.rating ? "fill-yellow-400 text-yellow-400" : "text-gray-300"
                    }`}
                  />
                ))}
              </div>
              <span className="text-sm font-medium">{review.rating}/4</span>
            </div>
            <h4 className="font-medium mb-2">{review.title}</h4>
          </div>
        </div>
      </CardHeader>
      <CardBody className="space-y-4">
        <p className="text-gray-600 leading-relaxed">{review.content}</p>

        {review.replies.length > 0 && (
          <div className="space-y-4">
            <Divider />
            <div className="space-y-4">
              {review.replies.map((reply) => (
                <div key={reply.id} className="flex gap-3 pl-4 border-l-2 border-gray-200">
                  <Avatar
                    src={reply.authorAvatar || "/placeholder.svg"}
                    name={reply.authorName}
                    size="sm"
                  />
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="font-medium text-sm">{reply.authorName}</span>
                      {reply.isBusinessOwner && (
                        <Chip size="sm" color="primary" variant="flat" className="text-xs">
                          Business
                        </Chip>
                      )}
                      <span className="text-xs text-gray-500">{reply.date}</span>
                    </div>
                    <p className="text-sm text-gray-600">{reply.content}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        <div className="flex items-center gap-2 pt-2">
          <Button
            variant="light"
            size="sm"
            onPress={() => setShowReplyForm(!showReplyForm)}
            startContent={<MessageCircle className="w-4 h-4" />}
          >
            Reply
          </Button>
          <span className="text-sm text-gray-500">
            {review.replies.length} {review.replies.length === 1 ? "reply" : "replies"}
          </span>
        </div>

        {showReplyForm && (
          <form onSubmit={handleReplySubmit} className="space-y-3 pt-2 border-t">
            <Input
              placeholder="Your name"
              value={replyAuthor}
              onValueChange={setReplyAuthor}
              isRequired
              variant="bordered"
            />
            <Textarea
              placeholder="Write your reply..."
              value={replyContent}
              onValueChange={setReplyContent}
              minRows={3}
              isRequired
              variant="bordered"
            />
            <div className="flex gap-2">
              <Button
                type="submit"
                size="sm"
                color="primary"
                startContent={<Send className="w-4 h-4" />}
              >
                Post Reply
              </Button>
              <Button
                type="button"
                variant="light"
                size="sm"
                onPress={() => setShowReplyForm(false)}
              >
                Cancel
              </Button>
            </div>
          </form>
        )}
      </CardBody>
    </Card>
  )
}
